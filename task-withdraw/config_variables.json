{"TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_ENDPOINTS": "localhost:50051", "TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_APIKEYS": "01JT41XVG16HNHZ2FCEJECT7AH", "TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_BALANCER": "round_robin", "TASK_WITHDRAW_GRPC_CLIENT_USER_SERVICE_TIMEOUT": 5000, "TASK_WITHDRAW_REDIS_DEFAULT_ADDRESS": "127.0.0.1:6379", "TASK_WITHDRAW_REDIS_DEFAULT_DB": 3, "TASK_WITHDRAW_REDIS_DEFAULT_PASS": "valkey_password", "TASK_WITHDRAW_REDIS_DEFAULT_IDLETIMEOUT": "20s", "TASK_WITHDRAW_LOGGER_PATH": "logs", "TASK_WITHDRAW_LOGGER_LEVEL": "all", "TASK_WITHDRAW_LOGGER_STDOUT": true, "TASK_WITHDRAW_LOGGER_ROTATESIZE": "100M", "TASK_WITHDRAW_LOGGER_ROTATEEXPIRE": "7d", "TASK_WITHDRAW_LOGGER_FORMAT": "json", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLED": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_SPEC": "*/10 * * * * *", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_BATCHSIZE": 50, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENABLED": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_BYPASSCHECK": false, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENERGYRESERVEPERCENT": 10.0, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ORDERTIMEOUTHOURS": 24, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_MAXRETRYCOUNT": 3, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_CHECKINTERVALSECONDS": 30, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIKEY": "A44137431E0D402AB441DB0E06B5D257", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APISECRET": "997EBBDC83C0161B8902F2CC1685DCA4F7F7B562118CC186599EC6834FFF4AAD", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIBASEURL": "https://itrx.io", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_ENERGYPERIOD": "1H", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_ETH": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_ERC20": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_TRX": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_TRC20": true, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_ERC20": "******************************************", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_TRC20": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_ETH": 18, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_ERC20": 6, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_TRX": 6, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_TRC20": 6, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEY": "e9568f7d468f88a306e91bc549136416b32dcfe07dff2aa6ef652f0f70db8320", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEYENVVAR": "WITHDRAW_HOT_ETH_PK", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_ADDRESS": "******************************************", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEY": "e762b5c4ec9b1c5e347f560e845d86b217c0ce80b74ce0d7d2d7b89524b43bc3", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEYENVVAR": "WITHDRAW_HOT_TRON_PK", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_ADDRESS": "TRfDoj9v6vv6QixPzoDf69LqoyrkTaxyMQ", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_ETH_URL": "https://blockchain.googleapis.com/v1/projects/kinetic-harbor-460313-n6/locations/us-central1/endpoints/ethereum-mainnet/rpc?key=AIzaSyDsLLHG_zaqWLMXXot-fKAzstGYBUwDzsM", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_ETH_CHAINID": 1, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_URL": "cool-responsive-brook.tron-mainnet.quiknode.pro:50051", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_APIKEY": "2279113c33d66b6b0cc5fe2d9970c47b51b27f3e", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_CALLTIMEOUTSECONDS": 15, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_MAXATTEMPTS": 30, "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_1": "insufficient hot wallet balance", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_2": "Invalid recipient address format", "TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_3": "exceeds single limit", "TASK_WITHDRAW_WITHDRAWALCONSUMER_ENABLED": true, "TASK_WITHDRAW_WITHDRAWALCONSUMER_REDISQUEUENAME": "queue:withdrawal_processing", "TASK_WITHDRAW_WITHDRAWALCONSUMER_CONCURRENCY": 1, "TASK_WITHDRAW_WITHDRAWALCONSUMER_DLQNAME": "queue:withdrawal_processing_dlq", "TASK_WITHDRAW_GRPCUPDATER_REDISQUEUENAME": "queue:withdrawal_status_update", "TASK_WITHDRAW_GRPCUPDATER_REDISDLQNAME": "queue:withdrawal_status_update_dlq", "TASK_WITHDRAW_GRPCUPDATER_BRPOPTIMEOUTSECONDS": 5, "TASK_WITHDRAW_GRPCUPDATER_MAXRETRIES": 5, "TASK_WITHDRAW_GRPCUPDATER_RETRYDELAYSECONDS": 10, "TASK_WITHDRAW_CONSUL_ADDRESS": "127.0.0.1:8500", "TASK_WITHDRAW_CONSUL_TOKEN": "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874", "TASK_WITHDRAW_CONSUL_CONFIG_PREFIX": "xpay/config"}